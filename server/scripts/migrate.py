#!/usr/bin/env python3
"""
Database migration script
"""
import sys
import os

# Add the parent directory to the path so we can import our modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.database.connection import create_tables, test_connections


def main():
    """Run database migrations"""
    print("🔄 Running database migrations...")
    
    # Test connections first
    if not test_connections():
        print("❌ Connection test failed. Please check your database and Redis configuration.")
        sys.exit(1)
    
    # Create tables
    try:
        create_tables()
        print("✅ Database tables created/updated successfully")
    except Exception as e:
        print(f"❌ Migration failed: {e}")
        sys.exit(1)
    
    print("🎉 Migration completed successfully!")


if __name__ == "__main__":
    main()
