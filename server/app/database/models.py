"""
SQLAlchemy database models
"""
from datetime import datetime, timezone
from typing import Optional
from sqlalchemy import String, DateTime, Text, Integer, JSON, Enum as SQLEnum
from sqlalchemy.orm import DeclarativeBase, Mapped, mapped_column
from sqlalchemy.dialects.postgresql import UUID
import uuid

from app.models.schemas import JobStatus, AIProvider


class Base(DeclarativeBase):
    pass


class Job(Base):
    __tablename__ = "jobs"

    # Primary key
    id: Mapped[str] = mapped_column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)

    # Job metadata
    status: Mapped[JobStatus] = mapped_column(SQLEnum(JobStatus), nullable=False, default=JobStatus.PENDING)
    provider: Mapped[AIProvider] = mapped_column(SQLEnum(AIProvider), nullable=False)

    # Timestamps
    created_at: Mapped[datetime] = mapped_column(DateTime, nullable=False, default=lambda: datetime.now(timezone.utc))
    started_at: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)
    completed_at: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)

    # Input data (stored as JSON)
    input_data: Mapped[dict] = mapped_column(JSON, nullable=False)
    webhook_url: Mapped[str] = mapped_column(String(500), nullable=False)

    # Results
    result_content: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    result_model: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)

    # Usage tracking
    prompt_tokens: Mapped[Optional[int]] = mapped_column(Integer, nullable=True, default=0)
    completion_tokens: Mapped[Optional[int]] = mapped_column(Integer, nullable=True, default=0)
    total_tokens: Mapped[Optional[int]] = mapped_column(Integer, nullable=True, default=0)

    # Error handling
    error_message: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    error_details: Mapped[Optional[dict]] = mapped_column(JSON, nullable=True)

    # Additional metadata
    processing_time_seconds: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)
    
    def __repr__(self):
        return f"<Job(id={self.id}, status={self.status}, provider={self.provider})>"
