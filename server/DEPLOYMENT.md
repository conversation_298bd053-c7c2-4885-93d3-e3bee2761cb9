# 🚀 Deployment Guide

## **Production Deployment**

### **Docker Compose (Recommended)**

1. **Prepare Environment**
```bash
# Copy environment template
cp .env.example .env

# Edit configuration
nano .env
```

2. **Start Services**
```bash
# Start all services in background
docker-compose up -d

# Check status
docker-compose ps

# View logs
docker-compose logs -f api
```

3. **Verify Deployment**
```bash
# Health check
curl http://localhost:8000/health

# API documentation
curl http://localhost:8000/docs
```

### **Environment Variables**

| Variable | Description | Default |
|----------|-------------|---------|
| `DATABASE_URL` | PostgreSQL connection string | `********************************************/truyenmoigiay` |
| `REDIS_URL` | Redis connection string | `redis://redis:6379/0` |
| `DEBUG` | Enable debug mode | `false` |
| `API_V1_PREFIX` | API prefix | `/api/v1` |
| `JOB_TIMEOUT_SECONDS` | Job processing timeout | `300` |
| `WEBHOOK_TIMEOUT_SECONDS` | Webhook request timeout | `30` |

### **Scaling**

```bash
# Scale API instances
docker-compose up -d --scale api=3

# Scale with load balancer (nginx example)
# Add nginx service to docker-compose.yml
```

### **Monitoring**

```bash
# View resource usage
docker stats

# Database queries
docker-compose exec postgres psql -U postgres -d truyenmoigiay -c "SELECT * FROM jobs LIMIT 5;"

# Redis cache
docker-compose exec redis redis-cli info memory
```

### **Backup & Recovery**

```bash
# Backup database
docker-compose exec postgres pg_dump -U postgres truyenmoigiay > backup.sql

# Restore database
docker-compose exec -T postgres psql -U postgres truyenmoigiay < backup.sql

# Backup Redis
docker-compose exec redis redis-cli BGSAVE
```

### **Troubleshooting**

```bash
# Check logs
docker-compose logs api
docker-compose logs postgres
docker-compose logs redis

# Restart services
docker-compose restart api

# Reset everything
docker-compose down -v
docker-compose up -d
```

## **Production Considerations**

### **Security**
- Use strong passwords for PostgreSQL
- Enable Redis authentication
- Use HTTPS in production
- Implement API rate limiting
- Store API keys securely (not in environment variables)

### **Performance**
- Configure PostgreSQL connection pooling
- Set appropriate Redis memory limits
- Use Redis clustering for high availability
- Monitor database query performance

### **High Availability**
- Use multiple API instances behind a load balancer
- Set up PostgreSQL replication
- Use Redis Sentinel or Cluster mode
- Implement health checks and auto-restart
