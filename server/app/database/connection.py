"""
Database connection and session management
"""
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import <PERSON><PERSON><PERSON>ool
import redis
from typing import Generator

from app.core.config import settings
from app.database.models import Base

# PostgreSQL Database
engine = create_engine(
    settings.database_url,
    pool_pre_ping=True,
    echo=settings.debug
)

SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Redis connection
redis_client = redis.from_url(settings.redis_url, decode_responses=True)


def create_tables():
    """Create all database tables"""
    Base.metadata.create_all(bind=engine)


def get_db() -> Generator[Session, None, None]:
    """Dependency to get database session"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


def get_redis() -> redis.Redis:
    """Get Redis client"""
    return redis_client


# Test connections
def test_connections():
    """Test database and Redis connections"""
    try:
        # Test PostgreSQL
        with engine.connect() as conn:
            conn.execute("SELECT 1")
        print("✅ PostgreSQL connection successful")
        
        # Test Redis
        redis_client.ping()
        print("✅ Redis connection successful")
        
        return True
    except Exception as e:
        print(f"❌ Connection test failed: {e}")
        return False
