"""
AI service implementations for OpenAI and Google Gemini
"""
import traceback
from datetime import datetime, timezone
import openai
import httpx
from google import genai
from google.genai import types

from app.models.schemas import OpenAIRequest, GeminiRequest, JobStatus
from app.services.job_service import JobService
from app.services.webhook_service import WebhookService


class AIService:
    def __init__(self, job_service: JobService, webhook_service: WebhookService):
        self.job_service = job_service
        self.webhook_service = webhook_service

    async def process_openai_request(self, job_id: str, request_data: OpenAIRequest):
        """Background task to process OpenAI API request"""
        started_at = datetime.now(timezone.utc)

        try:
            # Update job status to processing
            self.job_service.update_job_status(
                job_id, JobStatus.PROCESSING, started_at=started_at
            )

            # Initialize OpenAI client with explicit httpx client to avoid proxies issues
            http_client = httpx.Client()
            client = openai.OpenAI(
                api_key=request_data.api_key,
                http_client=http_client
            )

            # Make OpenAI API call
            response = client.chat.completions.create(
                model=request_data.model,
                messages=[{"role": "user", "content": request_data.prompt}],
                max_tokens=request_data.max_tokens,
                temperature=request_data.temperature
            )

            # Extract result
            content = response.choices[0].message.content or ""
            model = response.model

            # Extract usage information
            usage = response.usage
            prompt_tokens = usage.prompt_tokens if usage else 0
            completion_tokens = usage.completion_tokens if usage else 0
            total_tokens = usage.total_tokens if usage else 0

            completed_at = datetime.now(timezone.utc)

            # Update job with result
            self.job_service.update_job_result(
                job_id=job_id,
                content=content,
                model=model,
                prompt_tokens=prompt_tokens,
                completion_tokens=completion_tokens,
                total_tokens=total_tokens
            )

            # Update status to completed
            self.job_service.update_job_status(
                job_id, JobStatus.COMPLETED, completed_at=completed_at
            )

            # Send webhook notification
            await self.webhook_service.send_notification(job_id, request_data.webhook_url)

        except Exception as e:
            completed_at = datetime.now(timezone.utc)
            error_details = {
                "error_type": type(e).__name__,
                "traceback": traceback.format_exc(),
                "request_model": request_data.model,
                "request_prompt_length": len(request_data.prompt)
            }

            # Update job with error
            self.job_service.update_job_error(
                job_id=job_id,
                error_message=str(e),
                error_details=error_details
            )

            # Update status to failed
            self.job_service.update_job_status(
                job_id, JobStatus.FAILED, completed_at=completed_at
            )

            # Send webhook notification with error
            await self.webhook_service.send_notification(job_id, request_data.webhook_url)

    async def process_gemini_request(self, job_id: str, request_data: GeminiRequest):
        """Background task to process Google Gemini API request"""
        started_at = datetime.now(timezone.utc)

        try:
            # Update job status to processing
            self.job_service.update_job_status(
                job_id, JobStatus.PROCESSING, started_at=started_at
            )

            # Initialize Gemini client
            client = genai.Client(api_key=request_data.api_key)

            # Generate content
            response = client.models.generate_content(
                model=request_data.model,
                contents=request_data.prompt,
                config=types.GenerateContentConfig(
                    max_output_tokens=request_data.max_tokens,
                    temperature=request_data.temperature,
                )
            )

            # Extract result
            content = ""
            if response.candidates and len(response.candidates) > 0:
                candidate = response.candidates[0]
                if candidate.content and candidate.content.parts and len(candidate.content.parts) > 0:
                    content = candidate.content.parts[0].text or ""

            # Extract usage information
            usage_metadata = response.usage_metadata
            prompt_tokens = usage_metadata.prompt_token_count if usage_metadata and usage_metadata.prompt_token_count else 0
            completion_tokens = usage_metadata.candidates_token_count if usage_metadata and usage_metadata.candidates_token_count else 0
            total_tokens = usage_metadata.total_token_count if usage_metadata and usage_metadata.total_token_count else 0

            completed_at = datetime.now(timezone.utc)

            # Update job with result
            self.job_service.update_job_result(
                job_id=job_id,
                content=content,
                model=request_data.model,
                prompt_tokens=prompt_tokens,
                completion_tokens=completion_tokens,
                total_tokens=total_tokens
            )

            # Update status to completed
            self.job_service.update_job_status(
                job_id, JobStatus.COMPLETED, completed_at=completed_at
            )

            # Send webhook notification
            await self.webhook_service.send_notification(job_id, request_data.webhook_url)

        except Exception as e:
            completed_at = datetime.now(timezone.utc)
            error_details = {
                "error_type": type(e).__name__,
                "traceback": traceback.format_exc(),
                "request_model": request_data.model,
                "request_prompt_length": len(request_data.prompt)
            }

            # Update job with error
            self.job_service.update_job_error(
                job_id=job_id,
                error_message=str(e),
                error_details=error_details
            )

            # Update status to failed
            self.job_service.update_job_status(
                job_id, JobStatus.FAILED, completed_at=completed_at
            )

            # Send webhook notification with error
            await self.webhook_service.send_notification(job_id, request_data.webhook_url)
