"""
Application configuration settings
"""
import os
from typing import Optional
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    # Application
    app_name: str = "Truyen Moi Giay AI API"
    app_version: str = "1.0.0"
    debug: bool = False
    
    # Database
    database_url: str = "postgresql://user:password@localhost:5432/truyenmoigiay"
    
    # Redis
    redis_url: str = "redis://localhost:6379/0"
    
    # API Settings
    api_v1_prefix: str = "/api/v1"
    
    # CORS
    allowed_origins: list[str] = ["*"]
    
    # Job Settings
    job_timeout_seconds: int = 300  # 5 minutes
    webhook_timeout_seconds: int = 30
    
    class Config:
        env_file = ".env"
        case_sensitive = False


# Global settings instance
settings = Settings()
