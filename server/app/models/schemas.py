"""
Pydantic schemas for API requests and responses
"""
from datetime import datetime
from enum import Enum
from typing import Dict, Optional, Any
from pydantic import BaseModel, Field


class JobStatus(str, Enum):
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"


class AIProvider(str, Enum):
    OPENAI = "openai"
    GEMINI = "gemini"


class OpenAIRequest(BaseModel):
    prompt: str = Field(..., description="The prompt to send to OpenAI")
    model: str = Field(default="gpt-3.5-turbo", description="OpenAI model to use")
    max_tokens: Optional[int] = Field(default=1000, description="Maximum tokens in response")
    temperature: Optional[float] = Field(default=0.7, description="Temperature for response generation")
    webhook_url: str = Field(..., description="URL to send the result to when job completes")
    api_key: str = Field(..., description="OpenAI API key")


class GeminiRequest(BaseModel):
    prompt: str = Field(..., description="The prompt to send to Google Gemini")
    model: str = Field(default="gemini-pro", description="Gemini model to use")
    max_tokens: Optional[int] = Field(default=1000, description="Maximum tokens in response")
    temperature: Optional[float] = Field(default=0.7, description="Temperature for response generation")
    webhook_url: str = Field(..., description="URL to send the result to when job completes")
    api_key: str = Field(..., description="Google AI API key")


class JobResponse(BaseModel):
    job_id: str
    status: JobStatus
    provider: AIProvider
    message: str


class UsageInfo(BaseModel):
    prompt_tokens: int = 0
    completion_tokens: int = 0
    total_tokens: int = 0


class JobResult(BaseModel):
    content: str
    model: str
    usage: UsageInfo


class JobStatusResponse(BaseModel):
    job_id: str
    status: JobStatus
    provider: AIProvider
    created_at: datetime
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    input_data: Optional[Dict[str, Any]] = None
    result: Optional[JobResult] = None
    error: Optional[str] = None
    error_details: Optional[Dict[str, Any]] = None


class WebhookPayload(BaseModel):
    job_id: str
    status: JobStatus
    provider: AIProvider
    result: Optional[JobResult] = None
    error: Optional[str] = None
    error_details: Optional[Dict[str, Any]] = None
    completed_at: datetime


class JobListResponse(BaseModel):
    total_jobs: int
    jobs: list[JobStatusResponse]
    page: int = 1
    page_size: int = 50
