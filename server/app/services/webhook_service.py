"""
Webhook notification service
"""
import httpx
from typing import Optional

from app.services.job_service import JobService
from app.models.schemas import WebhookPayload
from app.core.config import settings


class WebhookService:
    def __init__(self, job_service: JobService):
        self.job_service = job_service
    
    async def send_notification(self, job_id: str, webhook_url: str):
        """Send webhook notification when job completes"""
        try:
            # Get job details
            job = self.job_service.get_job(job_id)
            if not job:
                print(f"Job {job_id} not found for webhook notification")
                return
            
            # Create webhook payload
            payload = WebhookPayload(
                job_id=job_id,
                status=job.status,
                provider=job.provider,
                result=job.result,
                error=job.error,
                error_details=job.error_details,
                completed_at=job.completed_at or job.created_at
            )
            
            # Send HTTP request
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    webhook_url,
                    json=payload.model_dump(),
                    timeout=settings.webhook_timeout_seconds
                )
                response.raise_for_status()
                
                print(f"✅ Webhook sent successfully for job {job_id}")
                
        except Exception as e:
            # Log webhook error but don't fail the job
            error_msg = f"Webhook notification failed for job {job_id}: {str(e)}"
            print(f"❌ {error_msg}")
            
            # Optionally store webhook failure in database
            # This could be useful for debugging webhook issues
