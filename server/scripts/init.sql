-- Initialize database with extensions and basic setup
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create indexes for better performance
-- These will be created automatically when the tables are created by SQLAlchemy
-- but we can add custom indexes here if needed

-- Example: Create index on job status for faster filtering
-- CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_jobs_status ON jobs(status);
-- CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_jobs_provider ON jobs(provider);
-- CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_jobs_created_at ON jobs(created_at DESC);
