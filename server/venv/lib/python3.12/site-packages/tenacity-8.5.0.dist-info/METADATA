Metadata-Version: 2.1
Name: tenacity
Version: 8.5.0
Summary: Retry code until it succeeds
Home-page: https://github.com/jd/tenacity
Author: <PERSON>
Author-email: <EMAIL>
License: Apache 2.0
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: Apache Software License
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3 :: Only
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Topic :: Utilities
Requires-Python: >=3.8
License-File: LICENSE
Provides-Extra: doc
Requires-Dist: reno ; extra == 'doc'
Requires-Dist: sphinx ; extra == 'doc'
Provides-Extra: test
Requires-Dist: pytest ; extra == 'test'
Requires-Dist: tornado >=4.5 ; extra == 'test'
Requires-Dist: typeguard ; extra == 'test'

Tenacity is a general-purpose retrying library to simplify the task of adding retry behavior to just about anything.
