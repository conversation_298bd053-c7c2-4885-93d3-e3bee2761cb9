"""
API routes for AI services
"""
from fastapi import APIRouter, BackgroundTasks, HTTPException, Depends, Query
from sqlalchemy.orm import Session
from typing import Optional

from app.models.schemas import (
    OpenAIRequest, GeminiRequest, JobResponse, JobStatusResponse, 
    JobListResponse, JobStatus, AIProvider
)
from app.database.connection import get_db
from app.services.job_service import JobService
from app.services.ai_service import AIService
from app.services.webhook_service import WebhookService

router = APIRouter()


def get_job_service(db: Session = Depends(get_db)) -> JobService:
    """Dependency to get job service"""
    return JobService(db)


def get_ai_service(job_service: JobService = Depends(get_job_service)) -> AIService:
    """Dependency to get AI service"""
    webhook_service = WebhookService(job_service)
    return AIService(job_service, webhook_service)


@router.post("/openai/submit", response_model=JobResponse)
async def submit_openai_request(
    request: OpenAIRequest,
    background_tasks: BackgroundTasks,
    job_service: JobService = Depends(get_job_service),
    ai_service: AIService = Depends(get_ai_service)
):
    """Submit an OpenAI API request for background processing"""
    
    # Prepare input data (exclude API key for security)
    input_data = request.model_dump(exclude={"api_key"})
    
    # Create job
    job_id = job_service.create_job(
        provider=AIProvider.OPENAI,
        input_data=input_data,
        webhook_url=request.webhook_url
    )
    
    # Add background task
    background_tasks.add_task(ai_service.process_openai_request, job_id, request)
    
    return JobResponse(
        job_id=job_id,
        status=JobStatus.PENDING,
        provider=AIProvider.OPENAI,
        message="OpenAI job submitted successfully. You will receive a webhook notification when complete."
    )


@router.post("/gemini/submit", response_model=JobResponse)
async def submit_gemini_request(
    request: GeminiRequest,
    background_tasks: BackgroundTasks,
    job_service: JobService = Depends(get_job_service),
    ai_service: AIService = Depends(get_ai_service)
):
    """Submit a Google Gemini API request for background processing"""
    
    # Prepare input data (exclude API key for security)
    input_data = request.model_dump(exclude={"api_key"})
    
    # Create job
    job_id = job_service.create_job(
        provider=AIProvider.GEMINI,
        input_data=input_data,
        webhook_url=request.webhook_url
    )
    
    # Add background task
    background_tasks.add_task(ai_service.process_gemini_request, job_id, request)
    
    return JobResponse(
        job_id=job_id,
        status=JobStatus.PENDING,
        provider=AIProvider.GEMINI,
        message="Gemini job submitted successfully. You will receive a webhook notification when complete."
    )


@router.get("/jobs/{job_id}", response_model=JobStatusResponse)
async def get_job_status(
    job_id: str,
    job_service: JobService = Depends(get_job_service)
):
    """Get the status of a submitted job"""
    
    job = job_service.get_job(job_id)
    if not job:
        raise HTTPException(status_code=404, detail="Job not found")
    
    return job


@router.get("/jobs", response_model=JobListResponse)
async def list_jobs(
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(50, ge=1, le=100, description="Items per page"),
    status: Optional[JobStatus] = Query(None, description="Filter by status"),
    provider: Optional[AIProvider] = Query(None, description="Filter by AI provider"),
    job_service: JobService = Depends(get_job_service)
):
    """List jobs with pagination and filtering"""
    
    jobs, total = job_service.list_jobs(
        page=page,
        page_size=page_size,
        status=status,
        provider=provider
    )
    
    return JobListResponse(
        total_jobs=total,
        jobs=jobs,
        page=page,
        page_size=page_size
    )
