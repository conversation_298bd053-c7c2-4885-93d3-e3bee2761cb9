# AI Background Job API

This FastAPI application provides endpoints for submitting OpenAI and Google Gemini AI API requests as background jobs with webhook notifications.

## 🏗️ **Enterprise Architecture**

The application is now containerized with a production-ready architecture:

### **📁 Project Structure**
```
server/
├── app/                     # Application package
│   ├── api/                 # API routes and endpoints
│   ├── core/                # Core configuration and settings
│   ├── database/            # Database models and connections
│   ├── models/              # Pydantic schemas
│   └── services/            # Business logic services
├── docker/                  # Docker-related files
├── scripts/                 # Utility scripts
├── main.py                  # FastAPI application entry point
├── Dockerfile               # Container definition
├── docker-compose.yml       # Multi-service orchestration
└── requirements.txt         # Python dependencies
```

### **🔧 Technology Stack**
- **FastAPI** - Modern Python web framework
- **PostgreSQL 16** - Primary database for job storage
- **Redis 7** - Caching and session management
- **SQLAlchemy** - ORM for database operations
- **Docker & Docker Compose** - Containerization
- **OpenAI API** - GPT models integration
- **Google Gemini AI** - Gemini models integration

## Features

- ✅ Submit OpenAI API requests for background processing
- ✅ Submit Google Gemini AI API requests for background processing
- ✅ Job status tracking with unique IDs
- ✅ Webhook notifications when jobs complete
- ✅ Error handling and proper status updates
- ✅ RESTful endpoints for job management

## 🚀 **Quick Start with Docker**

### **Prerequisites**
- Docker and Docker Compose installed
- At least 4GB RAM available

### **1. Clone and Setup**
```bash
git clone <repository>
cd server
cp .env.example .env
```

### **2. Start All Services**
```bash
# Start PostgreSQL, Redis, and API
docker-compose up -d

# View logs
docker-compose logs -f api
```

### **3. Verify Installation**
```bash
# Check health
curl http://localhost:8000/health

# View API documentation
open http://localhost:8000/docs
```

## 🛠️ **Development Setup**

### **Local Development**
```bash
# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Start services (PostgreSQL + Redis)
docker-compose up -d postgres redis

# Run migrations
python scripts/migrate.py

# Start development server
uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

## 📚 **API Endpoints**

All endpoints are prefixed with `/api/v1`

### **1. Submit OpenAI Request**
**POST** `/api/v1/openai/submit`

```json
{
  "prompt": "Write a short story about AI",
  "model": "gpt-3.5-turbo",
  "max_tokens": 1000,
  "temperature": 0.7,
  "webhook_url": "https://your-webhook.com/callback",
  "api_key": "your-openai-api-key"
}
```

### **2. Submit Google Gemini Request**
**POST** `/api/v1/gemini/submit`

```json
{
  "prompt": "Write a haiku about technology",
  "model": "gemini-pro",
  "max_tokens": 500,
  "temperature": 0.7,
  "webhook_url": "https://your-webhook.com/callback",
  "api_key": "your-google-ai-api-key"
}
```

### **3. Get Job Status**
**GET** `/api/v1/jobs/{job_id}`

Returns detailed job information including:
- Input data (excluding API keys)
- Generated content and usage statistics
- Error details if the job failed
- Processing timestamps

### **4. List Jobs**
**GET** `/api/v1/jobs?page=1&page_size=50&status=completed&provider=openai`

Query parameters:
- `page`: Page number (default: 1)
- `page_size`: Items per page (default: 50, max: 100)
- `status`: Filter by job status (pending, processing, completed, failed)
- `provider`: Filter by AI provider (openai, gemini)

**Response:**
```json
{
  "job_id": "123e4567-e89b-12d3-a456-426614174000",
  "status": "completed",
  "created_at": "2024-01-01T12:00:00",
  "completed_at": "2024-01-01T12:00:30",
  "result": {
    "content": "Generated text from OpenAI...",
    "model": "gpt-3.5-turbo",
    "usage": {
      "prompt_tokens": 10,
      "completion_tokens": 100,
      "total_tokens": 110
    }
  },
  "error": null
}
```

### 5. List All Jobs
**GET** `/jobs`

List all jobs (OpenAI and Gemini) for monitoring purposes.

## Webhook Payload

When a job completes (successfully or with error), a POST request will be sent to your webhook URL:

```json
{
  "job_id": "123e4567-e89b-12d3-a456-426614174000",
  "status": "completed",
  "result": {
    "content": "Generated text...",
    "model": "gpt-3.5-turbo",
    "usage": {...}
  },
  "error": null,
  "completed_at": "2024-01-01T12:00:30"
}
```

## Job Statuses

- `pending`: Job has been submitted and is waiting to be processed
- `processing`: Job is currently being processed by OpenAI API
- `completed`: Job completed successfully
- `failed`: Job failed due to an error

## Example Usage

```bash
# Submit OpenAI job
curl -X POST "http://localhost:8000/openai/submit" \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "Write a short story about AI",
    "model": "gpt-3.5-turbo",
    "max_tokens": 500,
    "webhook_url": "https://your-webhook.com/callback",
    "api_key": "your-openai-api-key"
  }'

# Submit Gemini job
curl -X POST "http://localhost:8000/gemini/submit" \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "Write a haiku about technology",
    "model": "gemini-pro",
    "max_tokens": 200,
    "webhook_url": "https://your-webhook.com/callback",
    "api_key": "your-google-ai-api-key"
  }'

# Check job status
curl "http://localhost:8000/openai/status/{job_id}"
curl "http://localhost:8000/gemini/status/{job_id}"
```

## Security Notes

- API keys are not stored in job records for security
- Consider implementing authentication for production use
- Use HTTPS for webhook URLs in production
- Consider rate limiting for production deployments

## Production Considerations

- Replace in-memory job storage with Redis or a database
- Add proper logging and monitoring
- Implement job cleanup/expiration
- Add authentication and authorization
- Use environment variables for configuration
