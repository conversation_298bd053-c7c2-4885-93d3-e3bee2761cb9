"""
Job management service with PostgreSQL and Redis
"""
import uuid
from datetime import datetime
from typing import Optional, List
from sqlalchemy.orm import Session
from sqlalchemy import desc

from app.database.models import Job
from app.database.connection import get_redis
from app.models.schemas import (
    JobStatus, AIProvider, JobStatusResponse, JobResult, UsageInfo
)


class JobService:
    def __init__(self, db: Session):
        self.db = db
        self.redis = get_redis()
    
    def create_job(
        self,
        provider: AIProvider,
        input_data: dict,
        webhook_url: str
    ) -> str:
        """Create a new job"""
        job_id = str(uuid.uuid4())
        
        # Create database record
        db_job = Job(
            id=job_id,
            provider=provider,
            input_data=input_data,
            webhook_url=webhook_url,
            status=JobStatus.PENDING
        )
        
        self.db.add(db_job)
        self.db.commit()
        self.db.refresh(db_job)
        
        # Cache in Redis for quick access
        self._cache_job_status(job_id, JobStatus.PENDING)
        
        return job_id
    
    def get_job(self, job_id: str) -> Optional[JobStatusResponse]:
        """Get job by ID"""
        db_job = self.db.query(Job).filter(Job.id == job_id).first()
        if not db_job:
            return None
        
        return self._job_to_response(db_job)
    
    def update_job_status(
        self,
        job_id: str,
        status: JobStatus,
        started_at: Optional[datetime] = None,
        completed_at: Optional[datetime] = None
    ):
        """Update job status"""
        db_job = self.db.query(Job).filter(Job.id == job_id).first()
        if not db_job:
            return False
        
        db_job.status = status
        if started_at:
            db_job.started_at = started_at
        if completed_at:
            db_job.completed_at = completed_at
            if db_job.started_at:
                processing_time = (completed_at - db_job.started_at).total_seconds()
                db_job.processing_time_seconds = int(processing_time)
        
        self.db.commit()
        
        # Update Redis cache
        self._cache_job_status(job_id, status)
        
        return True
    
    def update_job_result(
        self,
        job_id: str,
        content: str,
        model: str,
        prompt_tokens: int = 0,
        completion_tokens: int = 0,
        total_tokens: int = 0
    ):
        """Update job with successful result"""
        db_job = self.db.query(Job).filter(Job.id == job_id).first()
        if not db_job:
            return False
        
        db_job.result_content = content
        db_job.result_model = model
        db_job.prompt_tokens = prompt_tokens
        db_job.completion_tokens = completion_tokens
        db_job.total_tokens = total_tokens
        
        self.db.commit()
        return True
    
    def update_job_error(
        self,
        job_id: str,
        error_message: str,
        error_details: Optional[dict] = None
    ):
        """Update job with error information"""
        db_job = self.db.query(Job).filter(Job.id == job_id).first()
        if not db_job:
            return False
        
        db_job.error_message = error_message
        db_job.error_details = error_details
        
        self.db.commit()
        return True
    
    def list_jobs(
        self,
        page: int = 1,
        page_size: int = 50,
        status: Optional[JobStatus] = None,
        provider: Optional[AIProvider] = None
    ) -> tuple[List[JobStatusResponse], int]:
        """List jobs with pagination and filtering"""
        query = self.db.query(Job)
        
        if status:
            query = query.filter(Job.status == status)
        if provider:
            query = query.filter(Job.provider == provider)
        
        # Get total count
        total = query.count()
        
        # Apply pagination
        offset = (page - 1) * page_size
        jobs = query.order_by(desc(Job.created_at)).offset(offset).limit(page_size).all()
        
        return [self._job_to_response(job) for job in jobs], total
    
    def _job_to_response(self, db_job: Job) -> JobStatusResponse:
        """Convert database job to response schema"""
        result = None
        if db_job.result_content:
            result = JobResult(
                content=db_job.result_content,
                model=db_job.result_model or "",
                usage=UsageInfo(
                    prompt_tokens=db_job.prompt_tokens or 0,
                    completion_tokens=db_job.completion_tokens or 0,
                    total_tokens=db_job.total_tokens or 0
                )
            )
        
        return JobStatusResponse(
            job_id=str(db_job.id),
            status=db_job.status,
            provider=db_job.provider,
            created_at=db_job.created_at,
            started_at=db_job.started_at,
            completed_at=db_job.completed_at,
            input_data=db_job.input_data,
            result=result,
            error=db_job.error_message,
            error_details=db_job.error_details
        )
    
    def _cache_job_status(self, job_id: str, status: JobStatus):
        """Cache job status in Redis for quick lookup"""
        try:
            self.redis.setex(f"job_status:{job_id}", 3600, status.value)  # 1 hour TTL
        except Exception as e:
            print(f"Redis cache error: {e}")
    
    def get_cached_status(self, job_id: str) -> Optional[JobStatus]:
        """Get job status from Redis cache"""
        try:
            status = self.redis.get(f"job_status:{job_id}")
            return JobStatus(status) if status else None
        except Exception:
            return None
